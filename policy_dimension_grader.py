import os
import json
import time
import pandas as pd
import psycopg2
import google.generativeai as genai
from datetime import datetime
import sys
import re
import logging
import signal
from contextlib import contextmanager
from typing import List, Dict, Optional, Tuple, Any

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('policy_dimension_grader.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Load configuration from policy_config.json
CONFIG_FILE = "/opt/app/PolicyDW/policy_config.json"

def load_config() -> Dict[str, Any]:
    """Load configuration with error handling"""
    try:
        if not os.path.exists(CONFIG_FILE):
            raise FileNotFoundError(f"Configuration file not found: {CONFIG_FILE}")

        with open(CONFIG_FILE, "r", encoding='utf-8') as f:
            config = json.load(f)

        # Validate required configuration sections
        required_sections = ['database', 'ai']
        for section in required_sections:
            if section not in config:
                raise ValueError(f"Missing required configuration section: {section}")

        logger.info("Configuration loaded successfully")
        return config
    except Exception as e:
        logger.error(f"Failed to load configuration: {e}")
        raise

config = load_config()

# PostgreSQL Connection Details
DB_CONFIG = config["database"]

# AI Configuration with multiple API key support
AI_CONFIG = config.get("ai", {})

# Support both single key (legacy) and multiple keys (new)
GEMINI_API_KEYS = []
if "gemini_api_keys" in AI_CONFIG:
    # New format: list of keys
    GEMINI_API_KEYS = AI_CONFIG.get("gemini_api_keys", [])
elif "gemini_api_key" in AI_CONFIG:
    # Legacy format: single key
    single_key = AI_CONFIG.get("gemini_api_key", "")
    if single_key:
        GEMINI_API_KEYS = [single_key]

# Try to get API key from environment variable if not in config
if not GEMINI_API_KEYS:
    env_key = os.environ.get("GOOGLE_API_KEY", "")
    if env_key:
        GEMINI_API_KEYS = [env_key]

# Verify we have at least one API key
if not GEMINI_API_KEYS:
    logger.error("No Gemini API keys found in config file or environment variables.")
    logger.error("Please set API keys in policy_config.json as 'gemini_api_keys' array or as GOOGLE_API_KEY environment variable.")
    logger.error("Example config: \"gemini_api_keys\": [\"key1\", \"key2\", \"key3\"]")
    sys.exit(1)

# Validate API key formats (basic check)
valid_keys = []
for i, key in enumerate(GEMINI_API_KEYS):
    if len(key) < 20:
        logger.warning(f"API key {i+1} appears to be too short. Skipping.")
        continue
    valid_keys.append(key)

if not valid_keys:
    logger.error("No valid API keys found. Please check your API keys.")
    sys.exit(1)

GEMINI_API_KEYS = valid_keys
logger.info(f"Loaded {len(GEMINI_API_KEYS)} valid API key(s)")

AI_MODEL = AI_CONFIG.get("model", "gemini-2.0-flash-lite")
MAX_RETRIES = AI_CONFIG.get("max_retries", 5)
RETRY_DELAY = AI_CONFIG.get("retry_delay", 2)

# Get batch size from config (with default if not present)
# For gemini-2.0-flash-lite with 30 requests/minute limit, we use smaller batches
# Note: Each batch makes 1 API call (removed redundant test calls)
BATCH_SIZE = config.get("batch_size", 5)  # Conservative default for rate limit compliance

# Validate batch size for rate limit compliance
# With 30 requests/minute and 2-second intervals, max sustainable rate is ~25 requests/minute
# Conservative batch size ensures we don't hit rate limits
if BATCH_SIZE > 15:
    logger.warning(f"Batch size {BATCH_SIZE} may be too large for rate limits. Reducing to 10 for safety.")
    BATCH_SIZE = 10
elif BATCH_SIZE > 10:
    logger.info(f"Batch size {BATCH_SIZE} is acceptable but monitor for rate limit issues.")

logger.info(f"Using batch size: {BATCH_SIZE} policies per batch")

# API Key Management System
class APIKeyManager:
    """Manages multiple API keys with automatic rotation when daily limits are reached"""

    def __init__(self, api_keys: List[str], model_name: str):
        self.api_keys = api_keys
        self.model_name = model_name
        self.current_key_index = 0
        self.key_stats = {}
        self.models = {}

        # Initialize stats for each key
        for i, key in enumerate(api_keys):
            self.key_stats[i] = {
                'requests_today': 0,
                'successful_requests': 0,
                'failed_requests': 0,
                'day_start_time': time.time(),
                'last_request_time': 0,
                'consecutive_errors': 0,
                'is_exhausted': False,
                'exhausted_time': 0
            }

        # Initialize first API key
        self._configure_current_key()
        logger.info(f"API Key Manager initialized with {len(api_keys)} keys")

    def _configure_current_key(self) -> bool:
        """Configure the current API key and initialize model"""
        try:
            current_key = self.api_keys[self.current_key_index]
            genai.configure(api_key=current_key)
            self.models[self.current_key_index] = genai.GenerativeModel(self.model_name)

            key_num = self.current_key_index + 1
            logger.info(f"Configured API key #{key_num} (length: {len(current_key)} chars)")
            return True
        except Exception as e:
            logger.error(f"Failed to configure API key #{self.current_key_index + 1}: {e}")
            return False

    def get_current_model(self):
        """Get the current active model"""
        if self.current_key_index not in self.models:
            if not self._configure_current_key():
                return None
        return self.models[self.current_key_index]

    def rotate_to_next_key(self) -> bool:
        """Rotate to the next available API key"""
        original_index = self.current_key_index
        attempts = 0

        while attempts < len(self.api_keys):
            self.current_key_index = (self.current_key_index + 1) % len(self.api_keys)
            attempts += 1

            # Check if this key is available
            if not self._is_key_exhausted(self.current_key_index):
                if self._configure_current_key():
                    key_num = self.current_key_index + 1
                    logger.info(f"Rotated to API key #{key_num}")
                    return True

            # If we've tried all keys, return to original and fail
            if self.current_key_index == original_index:
                break

        logger.warning("All API keys are currently exhausted")
        return False

    def wait_for_quota_reset(self) -> bool:
        """Wait until at least one API key has its quota reset"""
        logger.info("All API keys exhausted. Calculating wait time until quota reset...")

        current_time = time.time()
        earliest_reset_time = float('inf')

        # Find the earliest quota reset time among all keys
        for i, stats in self.key_stats.items():
            # Calculate when this key's quota will reset (next day)
            day_start = stats['day_start_time']
            next_reset = day_start + 86400  # 24 hours later

            if next_reset < earliest_reset_time:
                earliest_reset_time = next_reset

        # Calculate wait time
        wait_seconds = earliest_reset_time - current_time

        if wait_seconds <= 0:
            # Quotas should have already reset, try to refresh
            logger.info("Quotas should have reset. Refreshing key status...")
            for i in range(len(self.api_keys)):
                self._is_key_exhausted(i)  # This will reset if it's a new day
            return True

        # Convert to hours and minutes for better readability
        wait_hours = int(wait_seconds // 3600)
        wait_minutes = int((wait_seconds % 3600) // 60)

        logger.info(f"⏰ Waiting {wait_hours}h {wait_minutes}m until quota reset...")
        logger.info(f"🔄 Processing will automatically resume at {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(earliest_reset_time))}")
        logger.info("💡 The system will continue running and automatically resume when quotas reset")
        logger.info("📊 You can monitor progress through the log messages below")

        # Wait in chunks to allow for periodic status updates
        chunk_size = 300  # 5 minutes
        total_waited = 0

        # Set up signal handler for graceful shutdown during wait
        shutdown_requested = False

        def signal_handler(signum, frame):
            nonlocal shutdown_requested
            logger.info("🛑 Shutdown signal received. Will stop waiting and exit gracefully...")
            shutdown_requested = True

        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

        while total_waited < wait_seconds and not shutdown_requested:
            remaining_wait = min(chunk_size, wait_seconds - total_waited)

            # Sleep in smaller chunks to check for shutdown signal
            for _ in range(int(remaining_wait // 10)):
                if shutdown_requested:
                    break
                time.sleep(10)

            if shutdown_requested:
                logger.info("Shutdown requested during quota wait. Exiting...")
                return False

            total_waited += remaining_wait

            # Update progress every 30 minutes
            if total_waited % 1800 == 0 or total_waited >= wait_seconds:
                remaining_seconds = wait_seconds - total_waited
                remaining_hours = int(remaining_seconds // 3600)
                remaining_minutes = int((remaining_seconds % 3600) // 60)

                if remaining_seconds > 0:
                    logger.info(f"⏳ Still waiting... {remaining_hours}h {remaining_minutes}m remaining until quota reset")
                    logger.info("💡 Press Ctrl+C to stop waiting and exit gracefully")
                else:
                    logger.info("🎉 Quota reset time reached! Checking for available keys...")

        # After waiting, refresh all key statuses
        logger.info("Refreshing API key statuses after quota reset...")
        available_keys = 0
        for i in range(len(self.api_keys)):
            if not self._is_key_exhausted(i):
                available_keys += 1

        if available_keys > 0:
            logger.info(f"✅ {available_keys} API key(s) now available after quota reset")
            return True
        else:
            logger.warning("No keys available after quota reset. This may indicate a configuration issue.")
            return False

    def _is_key_exhausted(self, key_index: int) -> bool:
        """Check if a specific key has reached its daily limit"""
        stats = self.key_stats[key_index]
        current_time = time.time()

        # Reset daily stats if it's a new day
        if current_time - stats['day_start_time'] >= 86400:
            stats['requests_today'] = 0
            stats['successful_requests'] = 0
            stats['failed_requests'] = 0
            stats['day_start_time'] = current_time
            stats['consecutive_errors'] = 0
            stats['is_exhausted'] = False
            stats['exhausted_time'] = 0
            logger.info(f"Daily quota reset for API key #{key_index + 1}")

        # Check if key is marked as exhausted and if cooldown period has passed
        if stats['is_exhausted']:
            # Allow retry after 1 hour cooldown
            if current_time - stats['exhausted_time'] > 3600:
                stats['is_exhausted'] = False
                stats['consecutive_errors'] = 0
                logger.info(f"API key #{key_index + 1} cooldown period ended, marking as available")
            else:
                return True

        # Check daily limit
        return stats['requests_today'] >= 1500

    def record_request_success(self):
        """Record a successful API request for the current key"""
        stats = self.key_stats[self.current_key_index]
        stats['requests_today'] += 1
        stats['successful_requests'] += 1
        stats['last_request_time'] = time.time()
        stats['consecutive_errors'] = 0

        # Log milestone usage
        if stats['requests_today'] % 100 == 0:
            key_num = self.current_key_index + 1
            logger.info(f"API key #{key_num}: {stats['requests_today']}/1500 requests used today")

    def record_request_failure(self, error: Exception):
        """Record a failed API request for the current key"""
        stats = self.key_stats[self.current_key_index]
        stats['requests_today'] += 1
        stats['failed_requests'] += 1
        stats['last_request_time'] = time.time()
        stats['consecutive_errors'] += 1

        error_str = str(error).lower()
        key_num = self.current_key_index + 1

        # Check if this is a quota exhaustion error
        if ("quota" in error_str and "exceeded" in error_str) or \
           ("limit" in error_str and "exceeded" in error_str) or \
           stats['requests_today'] >= 1500:
            logger.warning(f"API key #{key_num} appears to have reached daily limit")
            stats['is_exhausted'] = True
            stats['exhausted_time'] = time.time()
            return True  # Indicates key should be rotated

        # Check for rate limiting (temporary)
        if "429" in str(error) or "rate limit" in error_str:
            logger.warning(f"API key #{key_num} hit rate limit (temporary)")
            return False  # Don't rotate, just wait

        # If too many consecutive errors, mark as potentially problematic
        if stats['consecutive_errors'] >= 5:
            logger.warning(f"API key #{key_num} has {stats['consecutive_errors']} consecutive errors")
            stats['is_exhausted'] = True
            stats['exhausted_time'] = time.time()
            return True  # Rotate to next key

        return False

    def has_available_keys(self) -> bool:
        """Check if any API keys are currently available (not exhausted)"""
        for i in range(len(self.api_keys)):
            if not self._is_key_exhausted(i):
                return True
        return False

    def get_available_key_count(self) -> int:
        """Get the number of currently available API keys"""
        count = 0
        for i in range(len(self.api_keys)):
            if not self._is_key_exhausted(i):
                count += 1
        return count

    def get_usage_summary(self) -> str:
        """Get a summary of usage across all API keys"""
        summary = []
        total_requests = 0
        total_successful = 0
        available_keys = 0

        for i, stats in self.key_stats.items():
            key_num = i + 1
            total_requests += stats['requests_today']
            total_successful += stats['successful_requests']

            is_exhausted = self._is_key_exhausted(i)
            if not is_exhausted:
                available_keys += 1

            status = "EXHAUSTED" if is_exhausted else "ACTIVE"
            if i == self.current_key_index:
                status += " (CURRENT)"

            summary.append(f"Key #{key_num}: {stats['requests_today']}/1500 requests, "
                         f"{stats['successful_requests']} successful - {status}")

        overall_success_rate = (total_successful / total_requests * 100) if total_requests > 0 else 0
        summary.append(f"Overall: {total_successful}/{total_requests} requests successful ({overall_success_rate:.1f}%)")
        summary.append(f"Available keys: {available_keys}/{len(self.api_keys)}")

        return "\n".join(summary)

# Initialize API Key Manager
try:
    api_key_manager = APIKeyManager(GEMINI_API_KEYS, AI_MODEL)
    model = api_key_manager.get_current_model()
    if not model:
        raise Exception("Failed to initialize any API key")
    logger.info("API Key Manager configured successfully")
except Exception as e:
    logger.error(f"Error configuring API Key Manager: {e}")
    logger.error("Please check your API keys and internet connection")
    sys.exit(1)

# Create a connection to the PostgreSQL database with error handling
@contextmanager
def get_db_connection():
    """Context manager for database connections with proper error handling"""
    conn = None
    try:
        conn = psycopg2.connect(
            dbname=DB_CONFIG["dbname"],
            user=DB_CONFIG["user"],
            password=DB_CONFIG["password"],
            host=DB_CONFIG["host"],
            port=DB_CONFIG["port"],
            connect_timeout=30
        )
        yield conn
    except psycopg2.Error as e:
        logger.error(f"Database connection error: {e}")
        if conn:
            conn.rollback()
        raise
    except Exception as e:
        logger.error(f"Unexpected database error: {e}")
        if conn:
            conn.rollback()
        raise
    finally:
        if conn:
            conn.close()

# Legacy function for backward compatibility
def connect_db():
    """Legacy database connection function - use get_db_connection() context manager instead"""
    return psycopg2.connect(
        dbname=DB_CONFIG["dbname"],
        user=DB_CONFIG["user"],
        password=DB_CONFIG["password"],
        host=DB_CONFIG["host"],
        port=DB_CONFIG["port"],
        connect_timeout=30
    )

# Get unprocessed policies (those without dimension values)
def get_unprocessed_policies(batch_size: int) -> List[Tuple[int, str, str]]:
    """
    Fetch policies that need dimension analysis.

    Args:
        batch_size: Number of policies to fetch

    Returns:
        List of tuples containing (id, policy_title, full_article)
    """
    if batch_size <= 0:
        logger.warning("Invalid batch size provided")
        return []

    try:
        with get_db_connection() as conn:
            with conn.cursor() as cur:
                # Select policies that have been processed but don't have dimension values yet
                cur.execute("""
                    SELECT a.id, a.policy_title, a.full_article
                    FROM policy_data a
                    JOIN policy_data_processed b ON a.id = b.id
                    WHERE (b.target_clarity IS NULL OR
                           b.legal_binding IS NULL OR
                           b.financial_support IS NULL OR
                           b.implementation_mechanism IS NULL OR
                           b.duration IS NULL OR
                           b.supporting_policies IS NULL)
                    AND a.policy_title IS NOT NULL
                    AND a.full_article IS NOT NULL
                    AND LENGTH(TRIM(a.full_article)) > 50
                    ORDER BY a.id
                    LIMIT %s
                """, (batch_size,))
                policies = cur.fetchall()

                logger.info(f"Retrieved {len(policies)} unprocessed policies")
                return policies

    except Exception as e:
        logger.error(f"Error fetching unprocessed policies: {e}")
        return []

# Rate limiting tracking for gemini-2.0-flash-lite (30 requests per minute, 1500 per day)
_rate_limit_tracker = {
    'last_error_time': 0,
    'last_request_time': 0,
    'consecutive_errors': 0,
    'total_requests': 0,
    'successful_requests': 0,
    'requests_in_current_minute': 0,
    'minute_start_time': 0,
    'max_requests_per_minute': 30,
    'max_requests_per_day': 1500,  # Fixed: Added missing daily limit
    'min_seconds_between_requests': 2.0,  # Fixed: Correct calculation (60/30 = 2.0)
    'requests_today': 0,
    'day_start_time': time.time()  # Start of the day
}

def check_rate_limit_status() -> bool:
    """Check if we're likely rate limited based on recent errors and request timing"""
    current_time = time.time()
    last_error = _rate_limit_tracker['last_error_time']

    # If we had a rate limit error in the last 5 minutes, be cautious
    if current_time - last_error < 300 and _rate_limit_tracker['consecutive_errors'] > 0:
        return True

    # Check if we're approaching the rate limit
    minute_start = _rate_limit_tracker['minute_start_time']
    if current_time - minute_start >= 60:
        # Reset the minute counter
        _rate_limit_tracker['minute_start_time'] = current_time
        _rate_limit_tracker['requests_in_current_minute'] = 0

    # Check if we've hit the per-minute limit
    if _rate_limit_tracker['requests_in_current_minute'] >= _rate_limit_tracker['max_requests_per_minute']:
        return True

    return False

def wait_for_rate_limit_compliance() -> None:
    """Ensure we comply with rate limits before making a request"""
    current_time = time.time()
    last_request_time = _rate_limit_tracker['last_request_time']
    min_interval = _rate_limit_tracker['min_seconds_between_requests']

    # Calculate time since last request
    time_since_last_request = current_time - last_request_time

    # If we need to wait, do so
    if time_since_last_request < min_interval:
        wait_time = min_interval - time_since_last_request
        logger.info(f"Rate limit compliance: waiting {wait_time:.1f} seconds before next request")
        time.sleep(wait_time)

    # Check minute-based rate limiting
    minute_start = _rate_limit_tracker['minute_start_time']
    if current_time - minute_start >= 60:
        # Reset the minute counter
        _rate_limit_tracker['minute_start_time'] = current_time
        _rate_limit_tracker['requests_in_current_minute'] = 0

    # If we've hit the per-minute limit, wait until the next minute
    if _rate_limit_tracker['requests_in_current_minute'] >= _rate_limit_tracker['max_requests_per_minute']:
        wait_until_next_minute = 60 - (current_time - _rate_limit_tracker['minute_start_time'])
        if wait_until_next_minute > 0:
            logger.info(f"Per-minute rate limit reached. Waiting {wait_until_next_minute:.1f} seconds for next minute")
            time.sleep(wait_until_next_minute)
            _rate_limit_tracker['minute_start_time'] = time.time()
            _rate_limit_tracker['requests_in_current_minute'] = 0

    # Check daily limit
    day_start = _rate_limit_tracker['day_start_time']
    if current_time - day_start >= 86400:  # 24 hours in seconds
        # Reset the daily counter
        _rate_limit_tracker['day_start_time'] = current_time
        _rate_limit_tracker['requests_today'] = 0
        logger.info("Daily quota reset - starting new day")

    # If we've hit the daily limit, check if we can use API key rotation instead of exiting
    if _rate_limit_tracker['requests_today'] >= _rate_limit_tracker['max_requests_per_day']:
        logger.warning(f"Global rate limit tracker shows {_rate_limit_tracker['requests_today']} requests today")
        logger.info("Checking if other API keys are available...")

        # Try to rotate to another key first
        if api_key_manager and api_key_manager.rotate_to_next_key():
            logger.info("Successfully rotated to another API key with available quota")
            # Reset the global tracker since we're using a different key
            _rate_limit_tracker['requests_today'] = 0
            _rate_limit_tracker['day_start_time'] = current_time
        else:
            # All keys exhausted, wait for reset
            logger.warning("All API keys have reached daily limits")
            if api_key_manager and api_key_manager.wait_for_quota_reset():
                logger.info("Quota reset completed, resetting global tracker")
                _rate_limit_tracker['requests_today'] = 0
                _rate_limit_tracker['day_start_time'] = current_time
                # Try to rotate to an available key
                if not api_key_manager.rotate_to_next_key():
                    logger.error("Failed to find available key after quota reset")
                    sys.exit(3)  # Exit only if we truly can't recover
            else:
                logger.error("Failed to wait for quota reset")
                sys.exit(3)  # Exit only as last resort

def handle_rate_limit_error(error: Exception) -> None:
    """Handle rate limit errors with intelligent backoff"""
    current_time = time.time()
    _rate_limit_tracker['last_error_time'] = current_time
    _rate_limit_tracker['consecutive_errors'] += 1

    error_str = str(error)
    logger.warning(f"Rate limit or quota error detected: {error}")

    # Try to extract wait time from error message
    wait_time = 60  # Default wait time

    # Look for specific wait time in error message
    import re
    match = re.search(r'retry_delay\s*{\s*seconds:\s*(\d+)', error_str)
    if match:
        wait_time = int(match.group(1))
        logger.info(f"API suggests waiting {wait_time} seconds")

    # Add exponential backoff based on consecutive errors
    backoff_multiplier = min(2 ** (_rate_limit_tracker['consecutive_errors'] - 1), 8)
    final_wait_time = wait_time * backoff_multiplier

    logger.info(f"Pausing for {final_wait_time} seconds to respect API limits...")
    time.sleep(final_wait_time)

def record_successful_request() -> None:
    """Record a successful API request"""
    current_time = time.time()
    _rate_limit_tracker['consecutive_errors'] = 0
    _rate_limit_tracker['successful_requests'] += 1
    _rate_limit_tracker['total_requests'] += 1
    _rate_limit_tracker['last_request_time'] = current_time
    _rate_limit_tracker['requests_in_current_minute'] += 1
    _rate_limit_tracker['requests_today'] += 1

    # Initialize minute tracking if not set
    if _rate_limit_tracker['minute_start_time'] == 0:
        _rate_limit_tracker['minute_start_time'] = current_time

    # Record success in API key manager
    api_key_manager.record_request_success()

def record_failed_request(error: Exception = None) -> bool:
    """Record a failed API request and return True if key should be rotated"""
    current_time = time.time()
    _rate_limit_tracker['total_requests'] += 1
    _rate_limit_tracker['last_request_time'] = current_time
    _rate_limit_tracker['requests_in_current_minute'] += 1
    _rate_limit_tracker['requests_today'] += 1

    # Initialize minute tracking if not set
    if _rate_limit_tracker['minute_start_time'] == 0:
        _rate_limit_tracker['minute_start_time'] = current_time

    # Record failure in API key manager and check if rotation is needed
    should_rotate = False
    if error and api_key_manager:
        should_rotate = api_key_manager.record_request_failure(error)

    return should_rotate

def cleanup_rate_limit_tracker() -> None:
    """Clean up rate limit tracker to prevent memory leaks in long-running processes"""
    current_time = time.time()

    # Reset old error tracking (older than 1 hour)
    if current_time - _rate_limit_tracker['last_error_time'] > 3600:
        _rate_limit_tracker['consecutive_errors'] = 0
        _rate_limit_tracker['last_error_time'] = 0

    # Log current status for monitoring
    if _rate_limit_tracker['total_requests'] > 0 and _rate_limit_tracker['total_requests'] % 100 == 0:
        success_rate = (_rate_limit_tracker['successful_requests'] / _rate_limit_tracker['total_requests']) * 100
        logger.info(f"API usage summary: {_rate_limit_tracker['successful_requests']}/{_rate_limit_tracker['total_requests']} requests successful ({success_rate:.1f}%)")
        logger.info(f"Daily usage: {_rate_limit_tracker['requests_today']}/{_rate_limit_tracker['max_requests_per_day']} requests")

# Validation functions
def validate_dimension_scores(scores: Dict[str, int]) -> bool:
    """Validate that dimension scores are within expected ranges"""
    score_ranges = {
        'target_clarity': (0, 2),
        'legal_binding': (0, 3),
        'financial_support': (0, 2),
        'implementation_mechanism': (0, 2),
        'duration': (0, 2),
        'supporting_policies': (0, 2)
    }

    for dimension, (min_val, max_val) in score_ranges.items():
        if dimension in scores:
            score = scores[dimension]
            if not isinstance(score, int) or score < min_val or score > max_val:
                logger.warning(f"Invalid score for {dimension}: {score} (expected {min_val}-{max_val})")
                return False

    return True

# Call Gemini to analyze policy dimensions
def analyze_policy_dimensions(policy_texts: List[str], policy_titles: List[str]) -> Optional[List[Dict[str, Any]]]:
    """
    Analyze policy dimensions using Gemini AI.

    Args:
        policy_texts: List of policy text content
        policy_titles: List of policy titles

    Returns:
        List of dimension analysis results or None if failed
    """
    if not policy_texts or not policy_titles:
        logger.error("Empty policy texts or titles provided")
        return None

    if len(policy_texts) != len(policy_titles):
        logger.error(f"Mismatch between policy texts ({len(policy_texts)}) and titles ({len(policy_titles)})")
        return None

    # Ensure we comply with rate limits before making any requests
    wait_for_rate_limit_compliance()

    # First check if we're likely rate limited
    if check_rate_limit_status():
        logger.warning("API rate limit likely reached. Pausing before making a request...")
        time.sleep(60)  # Wait a minute before attempting

    # Removed redundant API test call to reduce API usage
    # The main request will serve as both test and actual processing
    
    # Create the prompt for Gemini
    prompt = f"""
    你是一个专业的中国低碳政策分析专家，擅长对政府政策文本进行客观分析和评分。

    我将提供 {len(policy_texts)} 条政策文本，请你对每条政策在以下6个维度进行评分，并返回一个CSV格式的表格（使用|分隔符）。

    **评分维度说明：**

    1. **目标明确性** (0-2分)：政策是否明确提出目标（如减排量、覆盖人群等）
       - 0分：没有明确目标
       - 1分：有一般性目标描述
       - 2分：有具体量化的目标

    2. **法律约束力** (0-3分)：政策是否具有法律效力，指令性还是建议性
       - 0分：纯建议性，无约束力
       - 1分：弱约束力，主要是倡导性
       - 2分：中等约束力，有明确要求但缺乏惩罚措施
       - 3分：强约束力，有明确的法律效力和惩罚措施

    3. **资金支持力度** (0-2分)：是否有财政支持资金，金额是否明确，有无资金渠道
       - 0分：无资金支持
       - 1分：有资金支持但金额或渠道不明确
       - 2分：有明确的资金支持金额和渠道

    4. **实施机制完善性** (0-2分)：是否有明确的执行机制，考核机制，考核机构和频次
       - 0分：无明确实施机制
       - 1分：有基本实施机制但不完善
       - 2分：有完善的实施机制、考核机制和负责机构

    5. **持续时间** (0-2分)：政策是一次性的还是具有持续性的支持
       - 0分：一次性或短期政策
       - 1分：中期政策（1-3年）
       - 2分：长期政策（3年以上）

    6. **配套政策支持** (0-2分)：是否有部门的联动、机制型工具跟进等配套政策支持
       - 0分：无配套政策支持
       - 1分：有少量配套政策支持
       - 2分：有完善的配套政策支持体系

    **返回格式要求：**
    - 返回一个CSV格式的表格，使用|作为分隔符
    - 表头为：policy_id|target_clarity|legal_binding|financial_support|implementation_mechanism|duration|supporting_policies
    - 每行对应一条政策的评分结果
    - 只返回数字评分，不要添加任何解释或评论
    - 确保返回 {len(policy_texts)} 行数据（不含表头）

    **政策文本：**
    """
    
    # Add each policy text to the prompt
    for i, (policy_id, title, text) in enumerate(zip(range(len(policy_texts)), policy_titles, policy_texts)):
        prompt += f"\n政策 {i+1} (ID: {policy_id}):\n标题: {title}\n内容:\n{text[:3000]}...\n" if len(text) > 3000 else f"\n政策 {i+1} (ID: {policy_id}):\n标题: {title}\n内容:\n{text}\n"
    
    prompt += "\n请直接返回CSV格式的评分结果（使用|分隔符），不要添加任何解释："
    
    try:
        # Ensure rate limit compliance before the main request
        wait_for_rate_limit_compliance()

        # Get current model (may trigger key rotation if needed)
        current_model = api_key_manager.get_current_model()
        if not current_model:
            logger.error("No available API keys")
            return None

        logger.info(f"Sending request to Gemini API for {len(policy_texts)} policies")
        response = current_model.generate_content(prompt)

        # Check for empty response
        if not response or not response.text:
            logger.error("Empty response received from API")
            should_rotate = record_failed_request()
            if should_rotate:
                logger.info("Attempting to rotate to next API key due to empty response")
                if api_key_manager.rotate_to_next_key():
                    logger.info("Successfully rotated to next API key")
                else:
                    logger.error("Failed to rotate to next API key - all keys may be exhausted")
            return None

        record_successful_request()

        # Clean up tracker periodically to prevent memory leaks
        cleanup_rate_limit_tracker()

        # Log raw response for debugging (truncated for security)
        response_preview = response.text[:500] + "..." if len(response.text) > 500 else response.text
        logger.debug(f"API Response preview: {response_preview}")
        
        # Parse the CSV response
        csv_output = response.text.strip()
        
        # Remove any markdown code block formatting if present
        if "```" in csv_output:
            csv_output = re.sub(r'```.*?\n', '', csv_output)
            csv_output = csv_output.replace("```", "").strip()
        
        # Parse the CSV data
        lines = csv_output.strip().split('\n')
        
        # Check if the first line is a header and skip it if so
        if lines[0].lower().startswith('policy_id') or lines[0].lower().startswith('id'):
            lines = lines[1:]
        
        results = []
        for i, line in enumerate(lines):
            if not line.strip():
                continue
                
            parts = line.split('|')
            if len(parts) >= 7:  # We need at least 7 parts (policy_id + 6 dimensions)
                try:
                    # Extract the scores, ensuring they're integers
                    policy_index = int(parts[0]) - 1  # Convert 1-based to 0-based index
                    if policy_index < 0 or policy_index >= len(policy_texts):
                        policy_index = i  # Use line index as fallback

                    result = {
                        'policy_id': policy_index,
                        'target_clarity': int(parts[1].strip()),
                        'legal_binding': int(parts[2].strip()),
                        'financial_support': int(parts[3].strip()),
                        'implementation_mechanism': int(parts[4].strip()),
                        'duration': int(parts[5].strip()),
                        'supporting_policies': int(parts[6].strip())
                    }

                    # Validate the scores
                    if validate_dimension_scores(result):
                        results.append(result)
                    else:
                        logger.warning(f"Invalid dimension scores in line {i+1}: {line}")

                except (ValueError, IndexError) as e:
                    logger.error(f"Error parsing line {i+1}: {line} - {e}")
        
        # Ensure we have the expected number of results
        if len(results) != len(policy_texts):
            logger.warning(f"Expected {len(policy_texts)} results but got {len(results)}")
            # If we have too few results, we'll return what we have and retry the rest later
            if len(results) == 0:
                logger.error("No valid results parsed from API response")
                return None

        logger.info(f"Successfully parsed {len(results)} dimension analysis results")
        return results

    except Exception as e:
        logger.error(f"Gemini API Error: {e}")
        should_rotate = record_failed_request(e)

        # Check if we should rotate to next API key
        if should_rotate:
            logger.warning("API key appears exhausted, attempting to rotate to next key")
            if api_key_manager.rotate_to_next_key():
                logger.info("Successfully rotated to next API key, will retry")
                # Don't return None immediately, let the retry logic handle it
            else:
                logger.warning("All API keys exhausted - will wait for quota reset")
                # Instead of failing, wait for quota reset
                if api_key_manager.wait_for_quota_reset():
                    logger.info("Quota reset completed, attempting to rotate to available key")
                    if api_key_manager.rotate_to_next_key():
                        logger.info("Successfully rotated to available key after quota reset")
                        # Let retry logic handle the actual retry
                    else:
                        logger.error("Failed to find available key even after quota reset")
                        return None
                else:
                    logger.error("Failed to wait for quota reset - this may indicate a system issue")
                    return None

        # Check if this is a rate limit error (temporary, don't rotate)
        if "429" in str(e) or "rate limit" in str(e).lower():
            handle_rate_limit_error(e)

        return None

# Update policy dimensions in the database
def update_policy_dimensions(policy_ids: List[int], dimension_results: List[Dict[str, Any]]) -> int:
    """
    Update policy dimensions in the database.

    Args:
        policy_ids: List of policy IDs to update
        dimension_results: List of dimension analysis results

    Returns:
        Number of successfully updated policies
    """
    if not dimension_results:
        logger.error("No valid dimension results to update")
        return 0

    if not policy_ids:
        logger.error("No policy IDs provided")
        return 0

    success_count = 0

    try:
        with get_db_connection() as conn:
            with conn.cursor() as cur:
                for i, result in enumerate(dimension_results):
                    if i >= len(policy_ids):
                        logger.warning(f"No policy ID available for result {i+1}")
                        continue

                    policy_id = policy_ids[i]

                    # Validate policy_id
                    if not isinstance(policy_id, int) or policy_id <= 0:
                        logger.error(f"Invalid policy ID: {policy_id}")
                        continue

                    # Update the policy dimensions
                    cur.execute("""
                        UPDATE policy_data_processed
                        SET
                            target_clarity = %s,
                            legal_binding = %s,
                            financial_support = %s,
                            implementation_mechanism = %s,
                            duration = %s,
                            supporting_policies = %s,
                            processed_at = CURRENT_TIMESTAMP
                        WHERE id = %s
                    """, (
                        result.get('target_clarity'),
                        result.get('legal_binding'),
                        result.get('financial_support'),
                        result.get('implementation_mechanism'),
                        result.get('duration'),
                        result.get('supporting_policies'),
                        policy_id
                    ))

                    # Check if the update affected any rows
                    if cur.rowcount == 0:
                        logger.warning(f"No rows updated for policy ID {policy_id} - policy may not exist in processed table")
                    else:
                        success_count += 1

                conn.commit()
                logger.info(f"Successfully updated dimensions for {success_count} policies")

    except Exception as e:
        logger.error(f"Error updating policy dimensions: {e}")
        raise

    return success_count

# Process a batch of policies
def process_policy_batch(policies: List[Tuple[int, str, str]]) -> int:
    """
    Process a batch of policies for dimension analysis.

    Args:
        policies: List of tuples containing (id, policy_title, full_article)

    Returns:
        Number of successfully processed policies
    """
    if not policies:
        logger.info("No policies to process")
        return 0

    policy_ids = [p[0] for p in policies]
    policy_titles = [p[1] for p in policies]
    policy_texts = [p[2] for p in policies]

    # Validate input data
    for i, (policy_id, title, text) in enumerate(policies):
        if not title or not text:
            logger.warning(f"Policy {policy_id} has empty title or text, skipping")
            continue
        if len(text.strip()) < 50:
            logger.warning(f"Policy {policy_id} has very short text ({len(text)} chars), may not be suitable for analysis")

    logger.info(f"Processing dimensions for {len(policies)} policies...")

    # Try to get AI analysis
    dimension_results = analyze_policy_dimensions(policy_texts, policy_titles)

    # Retry logic with exponential backoff and rate limit compliance
    retry_count = 0
    base_delay = RETRY_DELAY

    while dimension_results is None and retry_count < MAX_RETRIES:
        retry_count += 1

        # Calculate delay with exponential backoff, but ensure minimum rate limit compliance
        exponential_delay = base_delay * (2 ** retry_count)
        rate_limit_delay = _rate_limit_tracker['min_seconds_between_requests']
        delay = max(min(exponential_delay, 300), rate_limit_delay)  # Cap at 5 minutes, min rate limit compliance

        logger.warning(f"Failed to analyze dimensions. Retrying... ({retry_count}/{MAX_RETRIES})")
        logger.info(f"Waiting {delay} seconds before retry (includes rate limit compliance)...")
        time.sleep(delay)

        # Try again with shorter policy excerpts if needed
        if retry_count >= 2:
            logger.info("Retry strategy: Using shorter policy excerpts")
            # Truncate policy texts to reduce prompt size
            truncated_texts = [text[:2000] + "..." if len(text) > 2000 else text for text in policy_texts]
            dimension_results = analyze_policy_dimensions(truncated_texts, policy_titles)
        else:
            dimension_results = analyze_policy_dimensions(policy_texts, policy_titles)

    if dimension_results is None:
        logger.error("Maximum retries reached. Skipping this batch.")
        return 0

    # Update the database with the results
    try:
        success_count = update_policy_dimensions(policy_ids, dimension_results)
        return success_count
    except Exception as e:
        logger.error(f"Failed to update database: {e}")
        return 0

# Main function
def main() -> None:
    """Main function to run the policy dimension grader"""
    logger.info("🚀 Starting Policy Dimension Grader")

    # Log rate limit configuration
    logger.info(f"Rate limits: {_rate_limit_tracker['max_requests_per_minute']} requests/minute, "
                f"{_rate_limit_tracker['max_requests_per_day']} requests/day")
    logger.info(f"Minimum interval between requests: {_rate_limit_tracker['min_seconds_between_requests']} seconds")

    # Removed redundant API test to save quota
    # The first batch processing will serve as the API connection test
    logger.info("✅ API configuration loaded, starting processing...")

    total_processed = 0
    batch_count = 0

    try:
        while True:
            batch_count += 1
            logger.info(f"Starting batch {batch_count}")

            # Check if we have any available API keys before processing
            if not api_key_manager.has_available_keys():
                logger.warning("No API keys currently available. Waiting for quota reset...")
                if api_key_manager.wait_for_quota_reset():
                    logger.info("Quota reset completed. Resuming processing...")
                    # Try to rotate to an available key
                    if not api_key_manager.rotate_to_next_key():
                        logger.error("Failed to find available key after quota reset")
                        break
                else:
                    logger.error("Failed to wait for quota reset. Stopping processing.")
                    break

            # Get a batch of unprocessed policies
            policies = get_unprocessed_policies(BATCH_SIZE)

            if not policies:
                logger.info("✅ No more unprocessed policies found. Job complete!")
                break

            # Process the batch
            processed_count = process_policy_batch(policies)
            total_processed += processed_count

            # Log progress
            success_rate = (processed_count / len(policies)) * 100 if policies else 0
            logger.info(f"Batch {batch_count} complete: {processed_count}/{len(policies)} policies processed ({success_rate:.1f}% success rate)")
            logger.info(f"Total progress: {total_processed} policies processed so far")

            # Log API usage statistics
            total_requests = _rate_limit_tracker['total_requests']
            successful_requests = _rate_limit_tracker['successful_requests']
            requests_this_minute = _rate_limit_tracker['requests_in_current_minute']
            requests_today = _rate_limit_tracker['requests_today']

            if total_requests > 0:
                api_success_rate = (successful_requests / total_requests) * 100
                logger.info(f"Overall API success rate: {successful_requests}/{total_requests} ({api_success_rate:.1f}%)")
                logger.info(f"Requests in current minute: {requests_this_minute}/{_rate_limit_tracker['max_requests_per_minute']}")

            # Log detailed API key usage statistics
            if batch_count % 5 == 0:  # Every 5 batches, show detailed stats
                logger.info("=== API Key Usage Summary ===")
                logger.info(api_key_manager.get_usage_summary())
                logger.info("=============================")

            # Rate limit compliance delay is handled within the API calls
            # No additional delay needed here as we enforce it per request

    except KeyboardInterrupt:
        logger.warning("Processing interrupted by user. Progress has been saved.")
    except Exception as e:
        logger.error(f"Error in main processing loop: {e}", exc_info=True)
        raise

    logger.info(f"🎉 Processing complete! Total policies processed: {total_processed}")

    # Final API key usage summary
    logger.info("=== Final API Key Usage Summary ===")
    logger.info(api_key_manager.get_usage_summary())
    logger.info("=====================================")

if __name__ == "__main__":
    main()
