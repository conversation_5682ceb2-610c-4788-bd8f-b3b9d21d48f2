{"database": {"dbname": "mydb", "user": "xiuli", "password": "73Lxf1017", "host": "localhost", "port": 5432}, "paths": {"data_folder": "/home/<USER>/data", "loaded_folder": "/home/<USER>/loaded", "temp_csv": "/home/<USER>/temp_data.csv"}, "table_name": "policy_data", "batch_size": 5, "ai": {"gemini_api_keys": ["AIzaSyAdStyw3XJgG7VQX_E174h6wSQbnBpe62g", "AIzaSyDrFak69hQ4aWvDJJXr-malBSTghaqz09Y", "AIzaSyByNgicTFNyVDqtiZEYHXBuwJo7y60GAuA", "AIzaSyAEx49EdOVDF48QTiRX4syGZClKiOCpfsA", "AIzaSyBNHkZcX8R5AL49SvRh1-UqKF1sJ6hWqHU"], "model": "gemini-2.0-flash-lite", "max_retries": 5, "retry_delay": 2}}